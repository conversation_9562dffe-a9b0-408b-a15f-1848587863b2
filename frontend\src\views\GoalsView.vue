<template>
  <div class="goals-container">
    <!-- 顶部导航 -->
    <TopNavbar />

    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <div class="title-section">
            <h1 class="page-title">
              <span class="title-icon">🎯</span>
              健康目标
            </h1>
          </div>
          <button
            @click="showCreateModal = true"
            class="create-goal-btn"
            :class="{ 'pulse': !hasGoals }"
          >
            <span class="btn-icon">✨</span>
            创建目标
          </button>
        </div>
      </div>

      <!-- 统计概览卡片 -->
      <div class="stats-overview" v-if="hasGoals">
        <div class="stat-card active-goals">
          <div class="stat-icon">🎯</div>
          <div class="stat-content">
            <div class="stat-number">{{ activeGoalsCount }}</div>
            <div class="stat-label">活跃目标</div>
          </div>
        </div>
        <div class="stat-card completed-goals">
          <div class="stat-icon">🏆</div>
          <div class="stat-content">
            <div class="stat-number">{{ completedGoalsCount }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </div>
        <div class="stat-card streak">
          <div class="stat-icon">🔥</div>
          <div class="stat-content">
            <div class="stat-number">{{ currentStreak }}</div>
            <div class="stat-label">连续天数</div>
          </div>
        </div>
        <div class="stat-card progress">
          <div class="stat-icon">📈</div>
          <div class="stat-content">
            <div class="stat-number">{{ Math.round(overallProgress) }}%</div>
            <div class="stat-label">总体进度</div>
          </div>
        </div>
      </div>

      <!-- 目标列表 -->
      <div class="goals-section">
        <!-- 空状态 -->
        <div v-if="!hasGoals && !isLoading" class="empty-state">
          <div class="empty-illustration">
            <div class="floating-elements">
              <div class="element element-1">🎯</div>
              <div class="element element-2">💪</div>
              <div class="element element-3">🏃‍♂️</div>
              <div class="element element-4">🥗</div>
            </div>
          </div>
          <h3 class="empty-title">开始你的健康之旅</h3>
          <p class="empty-description">设定第一个健康目标，让我们一起追踪你的进步</p>
          <button @click="showCreateModal = true" class="start-journey-btn">
            <span class="btn-icon">🚀</span>
            开始设定目标
          </button>
        </div>

        <!-- 目标卡片网格 -->
        <div v-else class="goals-grid">
          <div
            v-for="(goal, index) in userGoals"
            :key="goal.id"
            class="goal-card"
            :class="[
              `goal-type-${goal.goalType}`,
              { 'completed': goal.isCompleted, 'overdue': goal.isOverdue }
            ]"
            :style="{ animationDelay: `${index * 0.1}s` }"
            @click="openGoalDetail(goal)"
          >
            <!-- 目标卡片头部 -->
            <div class="goal-header">
              <div class="goal-icon-wrapper">
                <span class="goal-icon">{{ getGoalIcon(goal.goalType) }}</span>
              </div>
              <div class="goal-meta">
                <h3 class="goal-title">{{ goal.goalName }}</h3>
                <span class="goal-category">{{ getGoalCategoryName(goal.goalType) }}</span>
              </div>
              <div class="goal-actions">
                <button @click.stop="editGoal(goal)" class="action-btn edit">
                  <span>✏️</span>
                </button>
                <button @click.stop="deleteGoal(goal.id)" class="action-btn delete">
                  <span>🗑️</span>
                </button>
              </div>
            </div>

            <!-- 进度环形图 -->
            <div class="progress-section">
              <div class="circular-progress" :style="getProgressStyle(goal.progress)">
                <svg class="progress-ring" width="120" height="120">
                  <circle
                    class="progress-ring-background"
                    cx="60"
                    cy="60"
                    r="50"
                    fill="transparent"
                    stroke="currentColor"
                    stroke-width="8"
                  />
                  <circle
                    class="progress-ring-progress"
                    cx="60"
                    cy="60"
                    r="50"
                    fill="transparent"
                    stroke="currentColor"
                    stroke-width="8"
                    :stroke-dasharray="circumference"
                    :stroke-dashoffset="getStrokeDashoffset(goal.progress)"
                    transform="rotate(-90 60 60)"
                  />
                </svg>
                <div class="progress-text">
                  <span class="progress-percentage">{{ Math.round(goal.progress) }}%</span>
                  <span class="progress-label">完成度</span>
                </div>
              </div>
            </div>

            <!-- 目标详情 -->
            <div class="goal-details">
              <div class="detail-row">
                <span class="detail-label">目标值</span>
                <span class="detail-value">{{ formatGoalValue(goal.targetValue, goal.unit) }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">当前值</span>
                <span class="detail-value current">{{ formatCurrentValue(goal) }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">截止日期</span>
                <span class="detail-value" :class="{ 'overdue': goal.isOverdue }">
                  {{ formatDate(goal.deadline) }}
                </span>
              </div>
            </div>

            <!-- 快速操作 -->
            <div class="quick-actions">
              <button @click.stop="updateProgress(goal)" class="quick-btn update">
                <span class="btn-icon">📊</span>
                更新进度
              </button>
              <button @click.stop="viewHistory(goal)" class="quick-btn history">
                <span class="btn-icon">📈</span>
                查看历史
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 目标创建/编辑模态框 -->
    <GoalModal
      :show="showCreateModal"
      :editing-goal="editingGoal"
      @close="closeModal"
      @submit="handleGoalSubmit"
    />

    <!-- 进度更新模态框 -->
    <ProgressUpdateModal
      :show="showProgressModal"
      :goal="progressGoal"
      @close="closeProgressModal"
      @submit="handleProgressSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import TopNavbar from '../components/TopNavbar.vue'
import GoalModal from '../components/GoalModal.vue'
import ProgressUpdateModal from '../components/ProgressUpdateModal.vue'
import { message } from '../utils/message'
import {
  getEnabledHealthGoals,
  getUserGoals,
  createUserGoal,
  updateUserGoal,
  deleteUserGoal,
  updateGoalProgress,
  getGoalStats,
  calculateProgress,
  formatGoalValue,
  isGoalOverdue,
  type UserGoal as BaseUserGoal,
  type HealthGoalOption,
  type CreateUserGoalRequest,
  type UpdateUserGoalRequest
} from '../utils/healthGoalApi'

// 扩展UserGoal类型，包含前端计算的属性
interface ExtendedUserGoal extends BaseUserGoal {
  isOverdue?: boolean
}

type UserGoal = ExtendedUserGoal

// 响应式数据
const isLoading = ref(false)
const showCreateModal = ref(false)
const showProgressModal = ref(false)
const editingGoal = ref<UserGoal | null>(null)
const progressGoal = ref<UserGoal | null>(null)
const userGoals = ref<UserGoal[]>([])
const healthGoalOptions = ref<HealthGoalOption[]>([])

// 统计数据
const stats = reactive({
  activeGoals: 0,
  completedGoals: 0,
  totalGoals: 0,
  overallProgress: 0,
  currentStreak: 0
})

const activeGoalsCount = computed(() => stats.activeGoals)
const completedGoalsCount = computed(() => stats.completedGoals)
const currentStreak = computed(() => stats.currentStreak)
const overallProgress = computed(() => stats.overallProgress)
const hasGoals = computed(() => userGoals.value.length > 0)

// 进度环形图相关
const circumference = 2 * Math.PI * 50 // 半径为50的圆周长

// 方法
const getGoalIcon = (type: string): string => {
  const icons = {
    calories: '🔥',
    weight: '⚖️',
    exercise: '💪',
    nutrition: '🥗',
    water: '💧',
    sleep: '😴'
  }
  return icons[type as keyof typeof icons] || '🎯'
}

const getGoalCategoryName = (type: string): string => {
  const names = {
    calories: '卡路里管理',
    weight: '体重管理',
    exercise: '运动健身',
    nutrition: '营养摄入',
    water: '水分补充',
    sleep: '睡眠质量'
  }
  return names[type as keyof typeof names] || '其他目标'
}

const getProgressStyle = (progress: number) => {
  const colors = {
    low: '#ef4444',      // 红色 0-30%
    medium: '#f59e0b',   // 橙色 30-70%
    high: '#10b981',     // 绿色 70-100%
  }

  let color = colors.low
  if (progress >= 70) color = colors.high
  else if (progress >= 30) color = colors.medium

  return {
    '--progress-color': color
  }
}

const getStrokeDashoffset = (progress: number): number => {
  return circumference - (progress / 100) * circumference
}

const formatCurrentValue = (goal: UserGoal): string => {
  return `${goal.currentValue} ${goal.unit}`
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = date.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays < 0) return '已过期'
  if (diffDays === 0) return '今天'
  if (diffDays === 1) return '明天'
  if (diffDays <= 7) return `${diffDays}天后`

  return date.toLocaleDateString('zh-CN')
}

// API调用方法
const loadHealthGoalOptions = async () => {
  try {
    const result = await getEnabledHealthGoals()
    if (result.success && result.data) {
      healthGoalOptions.value = result.data
    } else {
      console.error('API返回错误:', result.message)
      message.error('加载健康目标选项失败')
    }
  } catch (error) {
    console.error('加载健康目标选项失败:', error)
    message.error('网络连接失败，无法加载健康目标选项')
  }
}

const loadUserGoals = async () => {
  isLoading.value = true
  try {
    const result = await getUserGoals()
    if (result.success && result.data) {
      // 处理用户目标数据，计算进度和状态
      userGoals.value = result.data.map(goal => ({
        ...goal,
        progress: calculateProgress(goal.currentValue, goal.targetValue, goal.goalType),
        isCompleted: goal.currentValue >= goal.targetValue,
        isOverdue: isGoalOverdue(goal.deadline)
      }))
    } else {
      console.error('API返回错误:', result.message)
      message.error('加载目标数据失败')
    }
  } catch (error) {
    console.error('加载用户目标失败:', error)
    message.error('加载目标数据失败')
  } finally {
    isLoading.value = false
  }
}

const loadGoalStats = async () => {
  try {
    const result = await getGoalStats()
    if (result.success && result.data) {
      Object.assign(stats, result.data)
    }
  } catch (error) {
    console.error('加载目标统计失败:', error)
    // 统计数据加载失败不影响主要功能，静默处理
  }
}

// 目标操作方法
const openGoalDetail = (goal: UserGoal) => {
  // TODO: 打开目标详情模态框或跳转到详情页
  console.log('打开目标详情:', goal)
  message.info('目标详情功能开发中...')
}

const editGoal = (goal: UserGoal) => {
  editingGoal.value = goal
  showCreateModal.value = true
}

const deleteGoal = async (goalId: number) => {
  if (!confirm('确定要删除这个目标吗？')) return

  try {
    const result = await deleteUserGoal(goalId)
    if (result.success) {
      userGoals.value = userGoals.value.filter(goal => goal.id !== goalId)
      message.success('目标删除成功')
      // 重新加载统计数据
      await loadGoalStats()
    } else {
      message.error(result.message || '删除目标失败')
    }
  } catch (error) {
    console.error('删除目标失败:', error)
    message.error('删除目标失败')
  }
}

const updateProgress = (goal: UserGoal) => {
  progressGoal.value = goal
  showProgressModal.value = true
}

const viewHistory = (goal: UserGoal) => {
  // TODO: 打开历史记录模态框
  console.log('查看历史:', goal)
  message.info('历史记录功能开发中...')
}

// 模态框处理方法
const closeModal = () => {
  showCreateModal.value = false
  editingGoal.value = null
}

const closeProgressModal = () => {
  showProgressModal.value = false
  progressGoal.value = null
}

const handleProgressSubmit = async (newValue: number) => {
  if (!progressGoal.value) return

  try {
    const result = await updateGoalProgress(progressGoal.value.id, newValue)
    if (result.success && result.data) {
      // 更新本地数据
      const index = userGoals.value.findIndex(g => g.id === progressGoal.value!.id)
      if (index !== -1) {
        userGoals.value[index] = {
          ...result.data,
          progress: calculateProgress(result.data.currentValue, result.data.targetValue, result.data.goalType),
          isCompleted: result.data.currentValue >= result.data.targetValue,
          isOverdue: isGoalOverdue(result.data.deadline)
        }
      }
      message.success('进度更新成功')
      closeProgressModal()
      // 重新加载统计数据
      await loadGoalStats()
    } else {
      message.error(result.message || '进度更新失败')
    }
  } catch (error) {
    console.error('更新进度失败:', error)
    message.error('更新进度失败')
  }
}

const handleGoalSubmit = async (goalData: any) => {
  try {
    if (editingGoal.value) {
      // 编辑目标
      const updateData: UpdateUserGoalRequest = {
        targetValue: goalData.targetValue,
        currentValue: goalData.currentValue,
        deadline: goalData.deadline,
        description: goalData.description
      }

      const result = await updateUserGoal(editingGoal.value.id, updateData)
      if (result.success && result.data) {
        // 更新本地数据
        const index = userGoals.value.findIndex(g => g.id === editingGoal.value!.id)
        if (index !== -1) {
          userGoals.value[index] = {
            ...result.data,
            progress: calculateProgress(result.data.currentValue, result.data.targetValue, result.data.goalType),
            isCompleted: result.data.currentValue >= result.data.targetValue,
            isOverdue: isGoalOverdue(result.data.deadline)
          }
        }
        message.success('目标更新成功')
        closeModal()
        await loadGoalStats()
      } else {
        message.error(result.message || '目标更新失败')
      }
    } else {
      // 创建新目标
      const createData: CreateUserGoalRequest = {
        goalCode: goalData.goalType, // 使用目标类型作为代码
        goalType: goalData.goalType,
        targetValue: goalData.targetValue,
        unit: goalData.unit,
        deadline: goalData.deadline,
        description: goalData.description
      }

      const result = await createUserGoal(createData)
      if (result.success && result.data) {
        // 添加到本地数据
        const newGoal = {
          ...result.data,
          progress: calculateProgress(result.data.currentValue, result.data.targetValue, result.data.goalType),
          isCompleted: result.data.currentValue >= result.data.targetValue,
          isOverdue: isGoalOverdue(result.data.deadline)
        }
        userGoals.value.push(newGoal)
        message.success('目标创建成功')
        closeModal()
        await loadGoalStats()
      } else {
        message.error(result.message || '目标创建失败')
      }
    }
  } catch (error) {
    console.error('目标操作失败:', error)
    message.error('操作失败，请稍后重试')
  }
}

// 生命周期
onMounted(async () => {
  await Promise.all([
    loadHealthGoalOptions(),
    loadUserGoals(),
    loadGoalStats()
  ])
})
</script>

<style scoped>
/* CSS变量定义 - 与项目保持一致 */
:root {
  --primary-color: #16a085;
  --primary-light: #1abc9c;
  --primary-dark: #138d75;
  --secondary-color: #3498db;
  --accent-color: #e74c3c;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --text-light: #bdc3c7;
  --background-light: #f8f9fa;
  --background-white: #ffffff;
  --border-color: #e9ecef;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
}

/* 主容器 */
.goals-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow-x: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(22, 160, 133, 0.1) 0%, rgba(26, 188, 156, 0.05) 100%);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 300px;
  height: 300px;
  top: -150px;
  right: -150px;
  animation-delay: 0s;
}

.circle-2 {
  width: 200px;
  height: 200px;
  bottom: -100px;
  left: -100px;
  animation-delay: 2s;
}

.circle-3 {
  width: 150px;
  height: 150px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}



.main-content {
  position: relative;
  z-index: 1;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 3rem;
  animation: slideInDown 0.8s ease-out;
}

.header-content {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-xl);
  padding: 2rem;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 3.5rem;
  font-weight: 800;
  color: #2c3e50;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 1rem;
  background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-icon {
  font-size: 4rem;
  animation: bounce 2s infinite;
}



.create-goal-btn {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 8px 25px rgba(238, 90, 36, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.create-goal-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.create-goal-btn:hover::before {
  left: 100%;
}

.create-goal-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(238, 90, 36, 0.5);
}

.create-goal-btn.pulse {
  animation: pulse 2s infinite;
}

.btn-icon {
  font-size: 1.2rem;
}

/* 统计概览 */
.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
  animation: slideInUp 0.8s ease-out 0.2s both;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #16a085, #1abc9c);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  font-size: 3rem;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: linear-gradient(135deg, #16a085, #1abc9c);
  color: white;
  box-shadow: 0 4px 15px rgba(22, 160, 133, 0.3);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: #2d3748;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1rem;
  color: #718096;
  font-weight: 500;
}

/* 特定统计卡片颜色 */
.stat-card.active-goals .stat-icon {
  background: linear-gradient(135deg, #4299e1, #3182ce);
}

.stat-card.completed-goals .stat-icon {
  background: linear-gradient(135deg, #48bb78, #38a169);
}

.stat-card.streak .stat-icon {
  background: linear-gradient(135deg, #ed8936, #dd6b20);
}

.stat-card.progress .stat-icon {
  background: linear-gradient(135deg, #9f7aea, #805ad5);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 30px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideInUp 0.8s ease-out 0.4s both;
}

.empty-illustration {
  position: relative;
  height: 200px;
  margin-bottom: 2rem;
}

.floating-elements {
  position: relative;
  height: 100%;
}

.element {
  position: absolute;
  font-size: 3rem;
  animation: float 3s ease-in-out infinite;
}

.element-1 {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.element-2 {
  top: 10%;
  right: 20%;
  animation-delay: 0.5s;
}

.element-3 {
  bottom: 20%;
  left: 30%;
  animation-delay: 1s;
}

.element-4 {
  bottom: 10%;
  right: 30%;
  animation-delay: 1.5s;
}

.empty-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 1rem 0;
}

.empty-description {
  font-size: 1.2rem;
  color: #718096;
  margin: 0 0 2rem 0;
  line-height: 1.6;
}

.start-journey-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 1.2rem 2.5rem;
  border-radius: 50px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.8rem;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
}

.start-journey-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(102, 126, 234, 0.5);
}

/* 目标网格 */
.goals-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 2rem;
  animation: slideInUp 0.8s ease-out 0.4s both;
}

.goal-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 25px;
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  animation: slideInUp 0.6s ease-out both;
}

.goal-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.goal-card:hover::before {
  transform: scaleX(1);
}

.goal-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.goal-card.completed {
  background: linear-gradient(135deg, rgba(72, 187, 120, 0.1), rgba(56, 161, 105, 0.1));
  border-color: rgba(72, 187, 120, 0.3);
}

.goal-card.overdue {
  background: linear-gradient(135deg, rgba(245, 101, 101, 0.1), rgba(229, 62, 62, 0.1));
  border-color: rgba(245, 101, 101, 0.3);
}

/* 目标卡片头部 */
.goal-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.goal-icon-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #16a085, #1abc9c);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(22, 160, 133, 0.3);
}

.goal-icon {
  font-size: 1.8rem;
  color: white;
}

.goal-meta {
  flex: 1;
}

.goal-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 0.3rem 0;
  line-height: 1.2;
}

.goal-category {
  font-size: 0.9rem;
  color: #718096;
  font-weight: 500;
}

.goal-actions {
  display: flex;
  gap: 0.5rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.goal-card:hover .goal-actions {
  opacity: 1;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.action-btn.edit:hover {
  background: #4299e1;
  color: white;
}

.action-btn.delete:hover {
  background: #f56565;
  color: white;
}

/* 进度环形图 */
.progress-section {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
}

.circular-progress {
  position: relative;
  width: 120px;
  height: 120px;
}

.progress-ring {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.progress-ring-background {
  stroke: rgba(0, 0, 0, 0.1);
  stroke-width: 8;
}

.progress-ring-progress {
  stroke: var(--progress-color, #667eea);
  stroke-width: 8;
  stroke-linecap: round;
  transition: stroke-dashoffset 0.8s ease-in-out, stroke 0.3s ease;
  filter: drop-shadow(0 0 6px var(--progress-color, #667eea));
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.progress-percentage {
  display: block;
  font-size: 1.5rem;
  font-weight: 800;
  color: #2d3748;
  line-height: 1;
}

.progress-label {
  display: block;
  font-size: 0.8rem;
  color: #718096;
  margin-top: 0.2rem;
}

/* 目标详情 */
.goal-details {
  margin: 1.5rem 0;
  padding: 1.5rem;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 15px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 0.9rem;
  color: #718096;
  font-weight: 500;
}

.detail-value {
  font-size: 1rem;
  color: #2d3748;
  font-weight: 600;
}

.detail-value.current {
  color: var(--progress-color, #667eea);
}

.detail-value.overdue {
  color: #f56565;
}

/* 快速操作 */
.quick-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.quick-btn {
  flex: 1;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 1px solid rgba(102, 126, 234, 0.2);
  padding: 0.8rem 1rem;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.quick-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.quick-btn.update {
  background: rgba(72, 187, 120, 0.1);
  color: #48bb78;
  border-color: rgba(72, 187, 120, 0.2);
}

.quick-btn.update:hover {
  background: rgba(72, 187, 120, 0.2);
  box-shadow: 0 4px 12px rgba(72, 187, 120, 0.2);
}

.quick-btn.history {
  background: rgba(237, 137, 54, 0.1);
  color: #ed8936;
  border-color: rgba(237, 137, 54, 0.2);
}

.quick-btn.history:hover {
  background: rgba(237, 137, 54, 0.2);
  box-shadow: 0 4px 12px rgba(237, 137, 54, 0.2);
}

/* 目标类型特定样式 */
.goal-type-calories .goal-icon-wrapper {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.goal-type-weight .goal-icon-wrapper {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
}

.goal-type-exercise .goal-icon-wrapper {
  background: linear-gradient(135deg, #a8edea, #fed6e3);
  color: #2d3748 !important;
}

.goal-type-nutrition .goal-icon-wrapper {
  background: linear-gradient(135deg, #96fbc4, #f9f047);
  color: #2d3748 !important;
}

.goal-type-water .goal-icon-wrapper {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
}

.goal-type-sleep .goal-icon-wrapper {
  background: linear-gradient(135deg, #a29bfe, #6c5ce7);
}

/* 动画效果 */
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-15px);
  }
  70% {
    transform: translateY(-7px);
  }
  90% {
    transform: translateY(-3px);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 8px 25px rgba(238, 90, 36, 0.4);
  }
  50% {
    box-shadow: 0 8px 25px rgba(238, 90, 36, 0.6), 0 0 0 10px rgba(238, 90, 36, 0.1);
  }
  100% {
    box-shadow: 0 8px 25px rgba(238, 90, 36, 0.4);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    padding: 1.5rem;
  }

  .goals-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 1rem;
  }

  .page-title {
    font-size: 2.5rem;
  }

  .title-icon {
    font-size: 3rem;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
  }

  .stats-overview {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .stat-card {
    padding: 1.5rem;
  }

  .stat-icon {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .goals-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .goal-card {
    padding: 1.5rem;
  }

  .empty-state {
    padding: 3rem 1.5rem;
  }

  .empty-title {
    font-size: 2rem;
  }

  .floating-elements {
    height: 150px;
  }

  .element {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 2rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .title-icon svg {
    width: 32px;
    height: 32px;
  }

  .stats-overview {
    grid-template-columns: 1fr;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .circular-progress {
    width: 100px;
    height: 100px;
  }

  .progress-percentage {
    font-size: 1.2rem;
  }

  .quick-actions {
    flex-direction: column;
    gap: 0.8rem;
  }

  .create-goal-btn,
  .start-journey-btn {
    padding: 1rem 1.5rem;
    font-size: 1rem;
  }
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(102, 126, 234, 0.2);
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>

<style scoped>
.page-container {
  min-height: 100vh;
  background: #f8fafc;
  padding: 2rem;
}

.page-header {
  text-align: center;
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.page-header p {
  color: #64748b;
  font-size: 1.125rem;
  margin: 0;
}

.page-content {
  max-width: 600px;
  margin: 0 auto;
}

.placeholder-card {
  background: white;
  border-radius: 16px;
  padding: 3rem;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.placeholder-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.placeholder-card h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.placeholder-card p {
  color: #64748b;
  margin: 0 0 1rem 0;
}

.placeholder-card ul {
  text-align: left;
  color: #64748b;
  margin: 0 0 2rem 0;
}

.placeholder-card li {
  margin: 0.5rem 0;
}

.status-badge {
  display: inline-block;
  background: #fef3c7;
  color: #d97706;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}
</style>
